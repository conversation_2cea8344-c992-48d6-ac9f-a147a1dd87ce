"""
Main application entry point for the Trigger Service.

This module initializes and configures the FastAPI application with all
necessary middleware, routes, and startup/shutdown events.
"""

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from contextlib import asynccontextmanager

from src.utils.config import get_settings
from src.utils.logger import setup_logging, get_logger
from src.database.connection import init_database, close_database
from src.api.routes import triggers, webhooks, health
from src.api.middleware.error_handler import ErrorHandlerMiddleware
from src.api.middleware.auth import AuthMiddleware
from src.api.middleware.correlation import CorrelationMiddleware
from src.api.middleware.logging import LoggingMiddleware, PerformanceLoggingMiddleware

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.

    Args:
        app: FastAPI application instance
    """
    # Startup
    settings = get_settings()
    setup_logging(settings.log_level, settings.log_format)

    try:
        await init_database()
        yield
    finally:
        # Shutdown
        await close_database()


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    settings = get_settings()

    # Create FastAPI app with lifespan and security schemes
    app = FastAPI(
        title="Trigger Service",
        description="Event-based trigger service for workflow automation",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
        debug=settings.debug,
        # Add security schemes for Swagger UI
        openapi_tags=[
            {
                "name": "triggers",
                "description": "Trigger management operations",
            },
            {
                "name": "webhooks",
                "description": "Webhook processing endpoints",
            },
            {
                "name": "health",
                "description": "Health monitoring and metrics",
            },
        ],
    )

    # Define security schemes for Swagger UI
    from fastapi.openapi.utils import get_openapi

    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )

        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "API Key",
                "description": "Enter your API key as a Bearer token (e.g., 'abc' for development)",
            },
            "ApiKeyAuth": {
                "type": "apiKey",
                "in": "header",
                "name": "X-API-Key",
                "description": "Enter your API key in the X-API-Key header (e.g., 'abc' for development)",
            },
        }

        # Add security requirements to protected endpoints
        for path_item in openapi_schema["paths"].values():
            for operation in path_item.values():
                if isinstance(operation, dict) and "tags" in operation:
                    # Add security to all endpoints except health and webhooks
                    if (
                        operation.get("tags")
                        and "health" not in operation["tags"]
                        and "webhooks" not in operation["tags"]
                    ):
                        operation["security"] = [{"BearerAuth": []}, {"ApiKeyAuth": []}]

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # Simplified middleware setup - just CORS for now
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Allow all origins for testing
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )

    # Add exception handlers
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc):
        """Handle 404 errors with consistent format."""
        return JSONResponse(
            status_code=404,
            content={
                "error": {
                    "type": "NotFound",
                    "message": "The requested resource was not found",
                    "status_code": 404,
                }
            },
        )

    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc):
        """Handle 500 errors with consistent format."""
        logger.error(f"Internal server error: {str(exc)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "type": "InternalServerError",
                    "message": "An internal server error occurred",
                    "status_code": 500,
                }
            },
        )

    # Register routers
    app.include_router(health.router)
    app.include_router(triggers.router)
    app.include_router(webhooks.router)

    # Add root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "trigger-service",
            "version": "1.0.0",
            "status": "operational",
            "docs": "/docs",
            "health": "/api/v1/health",
        }

    logger.info("FastAPI application created and configured")
    return app


app = create_app()


def main() -> None:
    """Main entry point for the application."""
    settings = get_settings()
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    main()
