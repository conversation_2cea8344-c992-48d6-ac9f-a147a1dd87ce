"""
Google Calendar Adapter for the Trigger Service.

This module implements the Google Calendar adapter that monitors calendar events
via webhooks and transforms them into standardized trigger events.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

import httpx
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.core.auth_client import AuthClient
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler, RetryableError

logger = get_logger(__name__)


class GoogleCalendarError(Exception):
    """Base exception for Google Calendar adapter errors."""

    pass


class GoogleCalendarAuthError(GoogleCalendarError):
    """Authentication-related errors."""

    pass


class GoogleCalendarAPIError(GoogleCalendarError, RetryableError):
    """API-related errors that should trigger retry."""

    pass


class GoogleCalendarAdapter(BaseTriggerAdapter):
    """
    Google Calendar adapter for monitoring calendar events.

    This adapter integrates with Google Calendar API to:
    - Set up webhook subscriptions for calendar events
    - Process incoming webhook events
    - Transform calendar events into standardized trigger events
    - Manage subscription lifecycle and renewal
    """

    def __init__(self):
        """Initialize the Google Calendar adapter."""
        super().__init__("google_calendar")
        self.settings = get_settings()
        # TEMPORARY: Comment out AuthClient until Auth service is ready
        # self.auth_client = AuthClient()
        self.retry_handler = RetryHandler(
            retryable_exceptions=[GoogleCalendarAPIError, HttpError, ConnectionError]
        )
        self._webhook_subscriptions: Dict[UUID, Dict[str, Any]] = {}
        logger.info("GoogleCalendarAdapter initialized")

    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """
        Get the set of event types supported by this adapter.

        Returns:
            Set[TriggerEventType]: Supported event types
        """
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
            TriggerEventType.REMINDER,
        }

    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate Google Calendar adapter configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Required fields
            required_fields = ["calendar_id"]
            for field in required_fields:
                if field not in config:
                    logger.error(f"Missing required field: {field}")
                    return False

            # Validate calendar_id format
            calendar_id = config["calendar_id"]
            if not isinstance(calendar_id, str) or not calendar_id.strip():
                logger.error("calendar_id must be a non-empty string")
                return False

            # Optional fields validation
            if "event_filters" in config:
                event_filters = config["event_filters"]
                if not isinstance(event_filters, dict):
                    logger.error("event_filters must be a dictionary")
                    return False

            # Validate webhook_ttl if provided
            if "webhook_ttl" in config:
                webhook_ttl = config["webhook_ttl"]
                if not isinstance(webhook_ttl, int) or webhook_ttl <= 0:
                    logger.error("webhook_ttl must be a positive integer")
                    return False

            logger.debug(
                f"Configuration validation passed for calendar_id: {calendar_id}"
            )
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    async def setup_trigger(self, trigger_config: TriggerConfiguration) -> bool:
        """
        Set up a new Google Calendar trigger with webhook subscription.

        Args:
            trigger_config: Complete trigger configuration

        Returns:
            bool: True if setup was successful, False otherwise
        """
        try:
            logger.info(
                f"Setting up Google Calendar trigger {trigger_config.trigger_id}",
                calendar_id=trigger_config.config.get("calendar_id"),
                event_types=trigger_config.event_types,
            )

            # Get user credentials
            credentials = await self._get_user_credentials(trigger_config.user_id)
            if not credentials:
                logger.error(
                    f"Failed to get credentials for user {trigger_config.user_id}"
                )
                return False

            # Create Google Calendar service
            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error("Failed to create Google Calendar service")
                return False

            # Set up webhook subscription
            subscription_id = await self._create_webhook_subscription(
                service, trigger_config
            )
            if not subscription_id:
                logger.error("Failed to create webhook subscription")
                return False

            # Store subscription information
            self._webhook_subscriptions[trigger_config.trigger_id] = {
                "subscription_id": subscription_id,
                "calendar_id": trigger_config.config["calendar_id"],
                "user_id": trigger_config.user_id,
                "created_at": datetime.now(),
                "expires_at": datetime.now()
                + timedelta(seconds=trigger_config.config.get("webhook_ttl", 3600)),
            }

            logger.info(
                f"Successfully set up Google Calendar trigger {trigger_config.trigger_id}",
                subscription_id=subscription_id,
            )
            return True

        except Exception as e:
            logger.error(
                f"Failed to setup Google Calendar trigger {trigger_config.trigger_id}",
                error=str(e),
            )
            return False

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a Google Calendar trigger and its webhook subscription.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            logger.info(f"Removing Google Calendar trigger {trigger_id}")

            # Get subscription info
            subscription_info = self._webhook_subscriptions.get(trigger_id)
            if not subscription_info:
                logger.warning(f"No subscription found for trigger {trigger_id}")
                return True  # Already removed or never existed

            # Get user credentials
            credentials = await self._get_user_credentials(subscription_info["user_id"])
            if credentials:
                # Create Google Calendar service
                service = await self._create_calendar_service(credentials)
                if service:
                    # Remove webhook subscription
                    await self._remove_webhook_subscription(
                        service, subscription_info["subscription_id"]
                    )

            # Remove from local storage
            del self._webhook_subscriptions[trigger_id]

            logger.info(f"Successfully removed Google Calendar trigger {trigger_id}")
            return True

        except Exception as e:
            logger.error(
                f"Failed to remove Google Calendar trigger {trigger_id}",
                error=str(e),
            )
            return False

    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw Google Calendar webhook event.

        Args:
            raw_event: Raw event data from Google Calendar webhook

        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        try:
            logger.debug(
                "Processing Google Calendar webhook event", event_data=raw_event
            )

            # Handle verification events
            if raw_event.get("type") == "verification":
                logger.debug("Received verification event, ignoring")
                return None

            # Extract webhook headers for validation
            headers = raw_event.get("webhook_headers", {})

            # Validate webhook authenticity (basic validation)
            if not self._validate_webhook(headers, raw_event):
                logger.warning("Invalid webhook received, ignoring")
                return None

            # Parse the event data
            event_data = await self._parse_calendar_event(raw_event, headers)
            if not event_data:
                logger.debug("Event parsing failed or event should be ignored")
                return None

            # Create standardized trigger event
            trigger_event = TriggerEvent(
                event_id=event_data["event_id"],
                event_type=event_data["event_type"],
                source="google_calendar",
                timestamp=event_data["timestamp"],
                data=event_data["data"],
                metadata=event_data.get("metadata"),
            )

            logger.info(
                f"Successfully processed Google Calendar event {trigger_event.event_id}",
                event_type=trigger_event.event_type,
            )

            return trigger_event

        except Exception as e:
            logger.error(f"Failed to process Google Calendar event", error=str(e))
            return None

    async def _perform_health_check(self) -> bool:
        """
        Perform health check for Google Calendar adapter.

        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # Check if we can reach Google Calendar API
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://www.googleapis.com/calendar/v3/users/me/calendarList",
                    timeout=10.0,
                )
                # We expect 401 since we're not authenticated, but service should be reachable
                return response.status_code in [200, 401, 403]

        except Exception as e:
            logger.error(f"Google Calendar health check failed", error=str(e))
            return False

    async def _get_user_credentials(self, user_id: str) -> Optional[Credentials]:
        """
        Get Google Calendar credentials for a user.

        TEMPORARY: Loading from local token.json file instead of Auth service
        until Auth service is ready.

        Args:
            user_id: ID of the user

        Returns:
            Credentials: Google OAuth2 credentials, or None if not found
        """
        try:
            # TEMPORARY: Load credentials from local token.json file
            # TODO: Replace with Auth service integration when ready
            import json
            import os
            import asyncio
            from pathlib import Path

            # Look for token.json in current directory and project root
            token_paths = [
                Path("token.json"),
                Path("./token.json"),
                Path("../token.json"),
                Path("../../token.json"),
            ]

            token_file = None
            for path in token_paths:
                # Use asyncio to check file existence without blocking
                if await asyncio.to_thread(path.exists):
                    token_file = path
                    break

            if not token_file:
                logger.warning(
                    f"No token.json file found for user {user_id}. "
                    "Please create a token.json file with Google OAuth2 credentials. "
                    "Searched paths: {', '.join(str(p) for p in token_paths)}"
                )
                return None

            logger.info(f"Loading Google Calendar credentials from {token_file}")

            # Use asyncio to read file without blocking
            creds_data = await asyncio.to_thread(self._read_json_file, token_file)

            # Create Google OAuth2 credentials object
            credentials = Credentials(
                token=creds_data.get("token"),
                refresh_token=creds_data.get("refresh_token"),
                token_uri=creds_data.get(
                    "token_uri", "https://oauth2.googleapis.com/token"
                ),
                client_id=creds_data.get("client_id"),
                client_secret=creds_data.get("client_secret"),
                scopes=creds_data.get(
                    "scopes", ["https://www.googleapis.com/auth/calendar"]
                ),
            )

            # Check if token needs refresh
            if credentials.expired and credentials.refresh_token:
                logger.info(f"Refreshing expired token for user {user_id}")
                try:
                    # Use asyncio to refresh token without blocking
                    await asyncio.to_thread(credentials.refresh, Request())

                    # Save refreshed token back to file
                    creds_data.update(
                        {
                            "token": credentials.token,
                            "refresh_token": credentials.refresh_token,
                        }
                    )

                    # Use asyncio to write file without blocking
                    await asyncio.to_thread(
                        self._write_json_file, token_file, creds_data
                    )

                    logger.info("Token refreshed and saved to file")
                except Exception as refresh_error:
                    logger.error(f"Failed to refresh token: {refresh_error}")
                    return None
            elif credentials.expired:
                logger.error(
                    f"Token expired and no refresh token available for user {user_id}"
                )
                return None

            logger.info(f"Successfully loaded credentials for user {user_id}")
            return credentials

        except FileNotFoundError:
            logger.error(
                f"token.json file not found for user {user_id}. "
                "Please create a token.json file with Google OAuth2 credentials."
            )
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in token.json file: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to get credentials for user {user_id}", error=str(e))
            return None

    async def _create_calendar_service(self, credentials: Credentials):
        """
        Create Google Calendar API service instance.

        Args:
            credentials: Google OAuth2 credentials

        Returns:
            Google Calendar service instance, or None if failed
        """
        try:
            service = build("calendar", "v3", credentials=credentials)
            return service

        except Exception as e:
            logger.error(f"Failed to create Google Calendar service", error=str(e))
            return None

    async def _create_webhook_subscription(
        self, service, trigger_config: TriggerConfiguration
    ) -> Optional[str]:
        """
        Create a webhook subscription for calendar events.

        Args:
            service: Google Calendar service instance
            trigger_config: Trigger configuration

        Returns:
            str: Subscription ID if successful, None otherwise
        """
        try:
            calendar_id = trigger_config.config["calendar_id"]
            webhook_ttl = trigger_config.config.get("webhook_ttl", 3600)

            # Check if webhook URL is configured
            if (
                not hasattr(self.settings, "google_calendar_webhook_url")
                or not self.settings.google_calendar_webhook_url
            ):
                logger.error("Google Calendar webhook URL not configured")
                return None

            # Prepare webhook subscription request
            body = {
                "id": f"trigger-{trigger_config.trigger_id}",
                "type": "web_hook",
                "address": self.settings.google_calendar_webhook_url,
                "params": {
                    "ttl": str(webhook_ttl),
                },
            }

            # Create the subscription using retry handler with timeout
            result = await asyncio.wait_for(
                self.retry_handler.execute_async(
                    self._execute_watch_request, service, calendar_id, body
                ),
                timeout=30.0,  # 30 second timeout
            )

            if result:
                subscription_id = result.get("id")
                logger.info(
                    f"Created webhook subscription {subscription_id} for calendar {calendar_id}"
                )
                return subscription_id

            return None

        except asyncio.TimeoutError:
            logger.error("Timeout creating webhook subscription")
            return None
        except Exception as e:
            logger.error(f"Failed to create webhook subscription", error=str(e))
            return None

    async def _execute_watch_request(
        self, service, calendar_id: str, body: Dict[str, Any]
    ):
        """
        Execute the Google Calendar watch request.

        Args:
            service: Google Calendar service instance
            calendar_id: Calendar ID to watch
            body: Request body for the watch request

        Returns:
            Dict: Response from the watch request
        """
        try:
            # Execute the watch request
            request = service.events().watch(calendarId=calendar_id, body=body)
            result = request.execute()
            return result

        except HttpError as e:
            if e.resp.status in [500, 502, 503, 504]:
                # Retryable HTTP errors
                raise GoogleCalendarAPIError(f"Google Calendar API error: {str(e)}")
            else:
                # Non-retryable errors
                raise GoogleCalendarError(f"Google Calendar API error: {str(e)}")
        except Exception as e:
            raise GoogleCalendarAPIError(f"Unexpected error: {str(e)}")

    async def _remove_webhook_subscription(self, service, subscription_id: str) -> bool:
        """
        Remove a webhook subscription.

        Args:
            service: Google Calendar service instance
            subscription_id: ID of the subscription to remove

        Returns:
            bool: True if removal was successful
        """
        try:
            # Stop the webhook subscription
            body = {
                "id": subscription_id,
                "resourceId": subscription_id,  # May be needed for some subscriptions
            }

            request = service.channels().stop(body=body)
            request.execute()

            logger.info(f"Successfully removed webhook subscription {subscription_id}")
            return True

        except HttpError as e:
            if e.resp.status == 404:
                # Subscription already removed or doesn't exist
                logger.info(
                    f"Webhook subscription {subscription_id} not found (already removed)"
                )
                return True
            else:
                logger.error(
                    f"Failed to remove webhook subscription {subscription_id}",
                    error=str(e),
                )
                return False
        except Exception as e:
            logger.error(
                f"Failed to remove webhook subscription {subscription_id}", error=str(e)
            )
            return False

    def _validate_webhook(
        self, headers: Dict[str, str], event_data: Dict[str, Any]
    ) -> bool:
        """
        Validate Google Calendar webhook authenticity.

        Args:
            headers: Webhook headers
            event_data: Event data

        Returns:
            bool: True if webhook is valid
        """
        try:
            # Basic validation - check for required headers
            required_headers = ["x-goog-channel-id", "x-goog-resource-state"]
            for header in required_headers:
                if header not in headers:
                    logger.warning(f"Missing required header: {header}")
                    return False

            # Validate resource state
            resource_state = headers.get("x-goog-resource-state")
            valid_states = ["exists", "not_exists", "sync"]
            if resource_state not in valid_states:
                logger.warning(f"Invalid resource state: {resource_state}")
                return False

            logger.debug("Webhook validation passed")
            return True

        except Exception as e:
            logger.error(f"Webhook validation failed", error=str(e))
            return False

    async def _parse_calendar_event(
        self, raw_event: Dict[str, Any], headers: Dict[str, str]
    ) -> Optional[Dict[str, Any]]:
        """
        Parse Google Calendar webhook event into standardized format.

        Args:
            raw_event: Raw event data from webhook
            headers: Webhook headers

        Returns:
            Dict: Parsed event data, or None if event should be ignored
        """
        try:
            # Extract information from headers
            channel_id = headers.get("x-goog-channel-id", "")
            resource_state = headers.get("x-goog-resource-state", "")
            resource_id = headers.get("x-goog-resource-id", "")

            # Determine event type based on resource state
            event_type_mapping = {
                "exists": TriggerEventType.UPDATED,  # Event created or updated
                "not_exists": TriggerEventType.DELETED,  # Event deleted
                "sync": TriggerEventType.TRIGGERED,  # Sync event (ignore)
            }

            event_type = event_type_mapping.get(resource_state)
            if not event_type or event_type == TriggerEventType.TRIGGERED:
                logger.debug(f"Ignoring sync event with state: {resource_state}")
                return None

            # Generate event ID
            event_id = f"{channel_id}-{resource_id}-{datetime.now().isoformat()}"

            # Extract calendar information from channel ID
            # Channel ID format: "trigger-{trigger_id}"
            trigger_id_part = (
                channel_id.replace("trigger-", "")
                if channel_id.startswith("trigger-")
                else ""
            )

            # Build event data
            event_data = {
                "event_id": event_id,
                "event_type": event_type,
                "timestamp": datetime.now(),
                "data": {
                    "channel_id": channel_id,
                    "resource_state": resource_state,
                    "resource_id": resource_id,
                    "trigger_id": trigger_id_part,
                    "calendar_event": raw_event.get("calendar_event", {}),
                },
                "metadata": {
                    "webhook_headers": headers,
                    "raw_event": raw_event,
                },
            }

            # Apply event filters if configured
            if not self._should_process_event(event_data, raw_event):
                logger.debug("Event filtered out by configuration")
                return None

            logger.debug(f"Successfully parsed calendar event {event_id}")
            return event_data

        except Exception as e:
            logger.error(f"Failed to parse calendar event", error=str(e))
            return None

    def _should_process_event(
        self, event_data: Dict[str, Any], raw_event: Dict[str, Any]
    ) -> bool:
        """
        Check if event should be processed based on filters.

        Args:
            event_data: Parsed event data
            raw_event: Raw event data

        Returns:
            bool: True if event should be processed
        """
        try:
            # For now, process all events
            # In the future, this could check trigger-specific filters
            # like event title patterns, attendee filters, etc.

            # Basic validation - ensure we have required data
            if not event_data.get("event_id") or not event_data.get("event_type"):
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking event filters", error=str(e))
            return False

    async def get_subscription_info(self, trigger_id: UUID) -> Optional[Dict[str, Any]]:
        """
        Get webhook subscription information for a trigger.

        Args:
            trigger_id: Trigger ID

        Returns:
            Dict: Subscription information, or None if not found
        """
        return self._webhook_subscriptions.get(trigger_id)

    async def renew_subscription(self, trigger_id: UUID) -> bool:
        """
        Renew a webhook subscription that is about to expire.

        Args:
            trigger_id: Trigger ID

        Returns:
            bool: True if renewal was successful
        """
        try:
            subscription_info = self._webhook_subscriptions.get(trigger_id)
            if not subscription_info:
                logger.error(f"No subscription found for trigger {trigger_id}")
                return False

            # Get user credentials
            credentials = await self._get_user_credentials(subscription_info["user_id"])
            if not credentials:
                logger.error(f"Failed to get credentials for subscription renewal")
                return False

            # Create service
            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error("Failed to create service for subscription renewal")
                return False

            # Remove old subscription
            await self._remove_webhook_subscription(
                service, subscription_info["subscription_id"]
            )

            # Create new subscription (this would need trigger config)
            # For now, just log that renewal is needed
            logger.info(f"Subscription renewal needed for trigger {trigger_id}")

            return True

        except Exception as e:
            logger.error(
                f"Failed to renew subscription for trigger {trigger_id}", error=str(e)
            )
            return False

    def _read_json_file(self, file_path) -> dict:
        """
        Synchronous helper method to read JSON file.
        This is called via asyncio.to_thread to avoid blocking.
        """
        import json

        with open(file_path, "r") as f:
            return json.load(f)

    def _write_json_file(self, file_path, data: dict) -> None:
        """
        Synchronous helper method to write JSON file.
        This is called via asyncio.to_thread to avoid blocking.
        """
        import json

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)
